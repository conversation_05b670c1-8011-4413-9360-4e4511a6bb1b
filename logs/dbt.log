Installing dbt project and profile setup
Info Created .vscode/extensions.json with dbt extension recommendation
Success Project created successfully!
Info Project name: jaffle_shop
Info Project directory: jaffle_shop
Info Setting up your profile...
Info Creating new profile...
No dbt_cloud.yml found - proceeding without cloud pre-population
Success Profile written to /Users/<USER>/.dbt/profiles.yml
Validating profile inputs, adapters, and connection

dbt-fusion 2.0.0-preview.35
   Loading ~/.dbt/profiles.yml
 Debugging profile: dev
 Debugging dbt version: 2.0.0-preview.35
 Debugging platform: macos aarch64 (unix)
 Debugging adapter type: bigquery (remote)
 Debugging dependencies:
  git: OK
 Debugging connection:
  "database": "ict-b-watsont",
  "schema": "control-tower",
  "method": "oauth",
  "location": "us-east1",
  "dataproc_batch": null
 Debugging connection test: OK
  Debugged All checks passed!
  Finished 'init' target 'dev' in 4s 954ms
