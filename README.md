#dbt

Setup:

- Install VSCode / Cursor extesion (dbt)
- Select the extension in your side bar / extension bar
- You may have to regsiter it (free) and it should ask you to download the dbt-fusion CLI (do it)
- Setup your DBT profile

```bash
cursor ~/.dbt/profiles.yml
```

```
control-tower:
  target: dev
  outputs:
    dev:
      type: bigquery
      threads: 16
      database: YOUR_SANDBOX (example, ict-b-watsont)
      schema: controltower
      method: oauth
      location: US
      dataproc_batch: null
```

In /control-tower directory run `dbt build`
