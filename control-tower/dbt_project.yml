name: control-tower
profile: control-tower
seed-paths: ["seeds"]
model-paths: ["models"]
macro-paths: ["macros"]
clean-targets:
  - "target"
  - "dbt_packages"

seeds:
  # Builds seeds into '<your_schema_name>_raw'
  control-tower:
    +schema: raw

vars:
  tenants:
    [
      "acehardware_jef<PERSON><PERSON>a",
      "superior_uniform_eudora_ar",
      "drt_automation_dev",
    ]

on-run-start:
  - "{% do ensure_dataset_exists(target.project, var('tenant', target.schema), target.location or 'US') %}"

models:
  control-tower:
    marts:
      +materialized: view
      +schema: "{{ var('tenant', target.schema) }}"
