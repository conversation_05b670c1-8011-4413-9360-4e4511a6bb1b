#!/usr/bin/env python3
import json
from pathlib import Path
import sys

try:
    import jsonschema
except Exception:
    jsonschema = None

TENANTS_DIR = Path(__file__).resolve().parents[1] / 'tenants'

def validate():
    schema = json.loads((TENANTS_DIR / 'schema.json').read_text())
    errors = []
    for p in sorted(TENANTS_DIR.glob('*.json')):
        if p.name == 'schema.json':
            continue
        data = json.loads(p.read_text())
        if jsonschema is None:
            continue
        v = jsonschema.Draft7Validator(schema)
        errs = sorted(v.iter_errors(data), key=lambda e: e.path)
        for e in errs:
            errors.append(f'{p.name}: {e.message}')
    if errors:
        print('\n'.join(errors))
        return 1
    print('OK')
    return 0

if __name__ == '__main__':
    sys.exit(validate())

