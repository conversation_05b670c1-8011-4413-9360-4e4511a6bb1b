#!/usr/bin/env bash
set -euo pipefail

# Runs dbt build for each tenant json under tenants/

TENANT_DIR="$(cd "$(dirname "$0")"/.. && pwd)/tenants"

mapfile -t TENANTS < <(python3 "$(dirname "$0")/tenants.py" list)

for TENANT in "${TENANTS[@]}"; do
  echo "\n=== Building tenant: ${TENANT} ===\n"
  TENANT_JSON=$(python3 "$(dirname "$0")/tenants.py" get "${TENANT}")
  dbt build --vars "{tenant: '${TENANT}', tenant_config: ${TENANT_JSON}}" "$@"
done

