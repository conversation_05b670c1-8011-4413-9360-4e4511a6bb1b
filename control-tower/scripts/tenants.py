#!/usr/bin/env python3
import json
import sys
from pathlib import Path

TENANTS_DIR = Path(__file__).resolve().parents[1] / 'tenants'

def list_tenants():
    for p in sorted(TENANTS_DIR.glob('*.json')):
        if p.name == 'schema.json':
            continue
        print(p.stem)

def load_tenant(name: str):
    p = TENANTS_DIR / f'{name}.json'
    with p.open() as f:
        return json.load(f)

def main():
    if len(sys.argv) < 2:
        print('usage: tenants.py [list | get <name>]')
        sys.exit(1)
    cmd = sys.argv[1]
    if cmd == 'list':
        list_tenants()
    elif cmd == 'get':
        if len(sys.argv) < 3:
            print('usage: tenants.py get <name>')
            sys.exit(1)
        print(json.dumps(load_tenant(sys.argv[2])))
    else:
        print(f'unknown command: {cmd}')
        sys.exit(1)

if __name__ == '__main__':
    main()

