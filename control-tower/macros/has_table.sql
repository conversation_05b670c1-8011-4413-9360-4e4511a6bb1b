{% macro has_table(dataset, table_name) %}
  {# Returns true if the table exists in BigQuery #}
  {% set sql %}
    select 1
    from `{{ target.project }}`.`{{ dataset }}`.__TABLES__
    where table_id = '{{ table_name }}'
    limit 1
  {% endset %}
  {% set results = run_query(sql) %}
  {% if results is not none and results|length > 0 %}
    {{ return(true) }}
  {% else %}
    {{ return(false) }}
  {% endif %}
{% endmacro %}

