{% macro generate_schema_name(custom_schema_name, node) -%}
  {#
    Override default behavior so that when a custom schema is provided
    (e.g., via var('tenant')), dbt uses it verbatim instead of
    prefixing with target.schema (which causes fish_acme, etc.).
  #}
  {%- if custom_schema_name is none -%}
    {{ target.schema }}
  {%- else -%}
    {{ custom_schema_name }}
  {%- endif -%}
{%- endmacro %}

